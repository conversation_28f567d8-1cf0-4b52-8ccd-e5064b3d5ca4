// Detailed syntax checker for JSX file
const fs = require('fs');

try {
  const content = fs.readFileSync('PsMagic_edit.jsx', 'utf8');
  const lines = content.split('\n');

  let openBraces = 0;
  let openParens = 0;
  let inString = false;
  let stringChar = '';
  let inComment = false;
  let inMultiComment = false;

  for (let lineNum = 0; lineNum < lines.length; lineNum++) {
    const line = lines[lineNum];

    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      const nextChar = i < line.length - 1 ? line[i + 1] : '';
      const prevChar = i > 0 ? line[i - 1] : '';

      // Handle single line comments
      if (!inString && !inMultiComment && char === '/' && nextChar === '/') {
        inComment = true;
        continue;
      }

      // Handle multi-line comments
      if (!inString && !inComment && char === '/' && nextChar === '*') {
        inMultiComment = true;
        i++; // skip the *
        continue;
      }

      if (inMultiComment && char === '*' && nextChar === '/') {
        inMultiComment = false;
        i++; // skip the /
        continue;
      }

      // Skip if in comment
      if (inComment || inMultiComment) continue;

      // Handle strings
      if (!inString && (char === '"' || char === "'")) {
        inString = true;
        stringChar = char;
        continue;
      } else if (inString && char === stringChar && prevChar !== '\\') {
        inString = false;
        stringChar = '';
        continue;
      }

      // Skip counting inside strings
      if (inString) continue;

      // Count braces and parentheses
      if (char === '{') {
        openBraces++;
        console.log(`Line ${lineNum + 1}: Opening brace { (total: ${openBraces})`);
      }
      if (char === '}') {
        openBraces--;
        console.log(`Line ${lineNum + 1}: Closing brace } (total: ${openBraces})`);
      }
      if (char === '(') openParens++;
      if (char === ')') openParens--;

      // Check for negative counts (closing without opening)
      if (openBraces < 0) {
        console.error(`Syntax Error: Extra closing brace '}' at line ${lineNum + 1}`);
        process.exit(1);
      }
      if (openParens < 0) {
        console.error(`Syntax Error: Extra closing parenthesis ')' at line ${lineNum + 1}`);
        process.exit(1);
      }
    }

    // Reset single line comment at end of line
    inComment = false;
  }

  // Check for unclosed braces/parentheses
  if (openBraces > 0) {
    console.error(`Syntax Error: ${openBraces} unclosed brace(s) '{'`);
    process.exit(1);
  }
  if (openParens > 0) {
    console.error(`Syntax Error: ${openParens} unclosed parenthesis '('`);
    process.exit(1);
  }

  console.log('✓ Basic syntax check passed');
  console.log(`✓ File has ${lines.length} lines`);
  console.log('✓ All braces and parentheses are balanced');

} catch (error) {
  console.error('Error reading file:', error.message);
  process.exit(1);
}
